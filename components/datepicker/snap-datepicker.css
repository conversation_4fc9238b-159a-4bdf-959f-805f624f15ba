/**
 * SnapDatepicker Styles
 * Exact implementation matching design specifications
 */

.snap-datepicker-wrapper {
    position: relative;
    display: inline-block;
    width: 100%;
    font-family: 'Amazon Ember', Arial, sans-serif;
}

/* Input Wrapper */
.snap-datepicker-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
}

/* Input Field */
.snap-datepicker-input {
    width: 100%;
    padding: 12px 40px 12px 12px;
    border: 1px solid var(--border-color, #DCE0E5);
    border-radius: 6px;
    background: var(--bg-primary, #FFFFFF);
    color: var(--text-accent, #1B1D21);
    font-family: 'Amazon Ember', Arial, sans-serif;
    font-size: 14px;
    font-weight: 500;
    box-sizing: border-box;
    cursor: pointer;
    transition: all 0.2s ease;
}

.snap-datepicker-input::placeholder {
    color: var(--text-primary, #606F95);
    opacity: 0.7;
}

.snap-datepicker-input:focus {
    outline: none;
    border-color: var(--action-btn-bg, #470CED);
    box-shadow: 0 0 0 1px var(--action-btn-bg, #470CED);
}

.snap-datepicker-wrapper.open .snap-datepicker-input {
    border-color: var(--action-btn-bg, #470CED);
    box-shadow: 0 0 0 1px var(--action-btn-bg, #470CED);
}

/* Calendar Icon */
.snap-datepicker-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-primary, #606F95);
    cursor: pointer;
    transition: color 0.2s ease;
    pointer-events: none;
}

.snap-datepicker-wrapper.open .snap-datepicker-icon {
    color: var(--action-btn-bg, #470CED);
}

/* Main Dropdown Container */
.snap-datepicker-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    opacity: 0;
    transform: translateY(-8px);
    transition: all 0.2s ease;
    /* Override any inline styles that might be applied */
    box-shadow: none !important;
    border: none !important;
    border-radius: 0 !important;
    background: transparent !important;
}

.snap-datepicker-wrapper.open .snap-datepicker-dropdown {
    opacity: 1;
    transform: translateY(0);
}

/* Calendar Container - Default (Light Mode) */
.snap-datepicker-container {
    width: fit-content;
    background: #FFFFFF;
    border: 1px solid #E9EBF2 !important;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    box-sizing: border-box;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

/* Dark Mode Container */
[data-theme="dark"] .snap-datepicker-container,
.snap-grid.dark .snap-datepicker-container,
body.dark .snap-datepicker-container {
    background: #1A1D23;
    border: 1px solid #2F3341 !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

/* Month/Year Selector Section */
.snap-datepicker-selector-section {
    display: flex;
    justify-content: space-between;
    gap: 8px;
    width: 232px;
    height: 40px;
}

/* Use existing snap-dropdown styles */
.snap-datepicker-dropdown-month,
.snap-datepicker-dropdown-year {
    position: relative;
    cursor: pointer;
    user-select: none;
}

.snap-datepicker-dropdown-month {
    width: 121px;
}

.snap-datepicker-dropdown-year {
    width: 103px;
}

.snap-datepicker-dropdown-month .dropdown-header,
.snap-datepicker-dropdown-year .dropdown-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    height: 40px;
    background: transparent;
    border: 1px solid #DCE0E5;
    border-radius: 4px;
    box-sizing: border-box;
    font-family: 'Amazon Ember', Arial, sans-serif;
    font-size: 12px;
    font-weight: 500;
    line-height: 1.197em;
    color: #18181B;
}

/* Dark Mode Dropdowns */
[data-theme="dark"] .snap-datepicker-dropdown-month .dropdown-header,
[data-theme="dark"] .snap-datepicker-dropdown-year .dropdown-header,
.snap-grid.dark .snap-datepicker-dropdown-month .dropdown-header,
.snap-grid.dark .snap-datepicker-dropdown-year .dropdown-header,
body.dark .snap-datepicker-dropdown-month .dropdown-header,
body.dark .snap-datepicker-dropdown-year .dropdown-header {
    border-color: #2F3341;
    color: #FFFFFF;
}

.snap-datepicker-dropdown-month .dropdown-header img,
.snap-datepicker-dropdown-year .dropdown-header img {
    width: 15px;
    height: 15px;
    flex-shrink: 0;
    margin-right: 0;
}

/* Use existing dropdown menu styles */
.snap-datepicker-dropdown-month .dropdown-menu,
.snap-datepicker-dropdown-year .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background: #FFFFFF;
    border: 1.5px solid #DCE0E5;
    border-radius: 4px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1001;
}

/* Dark Mode Dropdown Menus */
[data-theme="dark"] .snap-datepicker-dropdown-month .dropdown-menu,
[data-theme="dark"] .snap-datepicker-dropdown-year .dropdown-menu,
.snap-grid.dark .snap-datepicker-dropdown-month .dropdown-menu,
.snap-grid.dark .snap-datepicker-dropdown-year .dropdown-menu,
body.dark .snap-datepicker-dropdown-month .dropdown-menu,
body.dark .snap-datepicker-dropdown-year .dropdown-menu {
    background: #1A1D23;
    border: 1.5px solid #2F3341;
}

.snap-datepicker-dropdown-month .dropdown-menu.hidden,
.snap-datepicker-dropdown-year .dropdown-menu.hidden {
    display: none;
}

.snap-datepicker-dropdown-month .dropdown-item,
.snap-datepicker-dropdown-year .dropdown-item {
    padding: 8px 12px;
    cursor: pointer;
    font-family: 'Amazon Ember', sans-serif;
    font-weight: 500;
    font-size: 12px;
    color: var(--text-primary);
    transition: background-color 0.15s ease;
}

.snap-datepicker-dropdown-month .dropdown-item:hover,
.snap-datepicker-dropdown-year .dropdown-item:hover {
    background: #F5F5F5;
}

/* Dark Mode Dropdown Items */
[data-theme="dark"] .snap-datepicker-dropdown-month .dropdown-item,
[data-theme="dark"] .snap-datepicker-dropdown-year .dropdown-item,
.snap-grid.dark .snap-datepicker-dropdown-month .dropdown-item,
.snap-grid.dark .snap-datepicker-dropdown-year .dropdown-item,
body.dark .snap-datepicker-dropdown-month .dropdown-item,
body.dark .snap-datepicker-dropdown-year .dropdown-item {
    color: #FFFFFF;
}

[data-theme="dark"] .snap-datepicker-dropdown-month .dropdown-item:hover,
[data-theme="dark"] .snap-datepicker-dropdown-year .dropdown-item:hover,
.snap-grid.dark .snap-datepicker-dropdown-month .dropdown-item:hover,
.snap-grid.dark .snap-datepicker-dropdown-year .dropdown-item:hover,
body.dark .snap-datepicker-dropdown-month .dropdown-item:hover,
body.dark .snap-datepicker-dropdown-year .dropdown-item:hover {
    background: #2F3341;
}

.snap-datepicker-dropdown-month .dropdown-item.selected,
.snap-datepicker-dropdown-year .dropdown-item.selected {
    background: #F3F4F6;
    color: var(--action-btn-bg);
}

/* Day Headers Row */
.snap-datepicker-header-row {
    display: flex;
    gap: 6px;
}

.snap-datepicker-day-header {
    width: 28px;
    height: 28px;
    background: #F9FAFB;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Amazon Ember', Arial, sans-serif;
    font-size: 14px;
    font-weight: 500;
    line-height: 2em;
    color: #656A78;
}

/* Dark Mode Day Headers */
[data-theme="dark"] .snap-datepicker-day-header,
.snap-grid.dark .snap-datepicker-day-header,
body.dark .snap-datepicker-day-header {
    background: #323741;
}

/* Calendar Body */
.snap-datepicker-body {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.snap-datepicker-row {
    display: flex;
    gap: 6px;
}

/* Day Cells - Default (Light Mode) */
.snap-datepicker-day {
    width: 28px;
    height: 28px;
    border: none;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Amazon Ember', Arial, sans-serif;
    font-size: 14px;
    font-weight: 400;
    line-height: 2em;
    text-align: center;
    cursor: pointer;
    position: relative;
}

/* Day Cell States - Default (Light Mode) */
.snap-datepicker-day.default {
    background: #F5F5F5;
    color: #606F95;
}

.snap-datepicker-day.default:hover {
    background: rgba(71, 12, 237, 0.2);
    border: 1.5px solid var(--action-btn-bg);
    color: var(--action-btn-bg);
}

.snap-datepicker-day.selected {
    background: var(--action-btn-bg);
    color: #FFFFFF;
}

.snap-datepicker-day.disabled {
    background: transparent;
    color: #C4C4C4;
    cursor: not-allowed;
}

.snap-datepicker-day.disabled:hover {
    background: transparent;
    border: none;
    color: #C4C4C4;
}

/* Dark Mode Day States */
[data-theme="dark"] .snap-datepicker-day.default,
.snap-grid.dark .snap-datepicker-day.default,
body.dark .snap-datepicker-day.default {
    background: #1A1D23;
    color: #FFFFFF;
}

[data-theme="dark"] .snap-datepicker-day.default:hover,
.snap-grid.dark .snap-datepicker-day.default:hover,
body.dark .snap-datepicker-day.default:hover {
    background: rgba(71, 12, 237, 0.2);
    border: 2px solid var(--action-btn-bg);
}

[data-theme="dark"] .snap-datepicker-day.disabled,
.snap-grid.dark .snap-datepicker-day.disabled,
body.dark .snap-datepicker-day.disabled {
    background: #2A2F37;
    color: #656A78;
}

[data-theme="dark"] .snap-datepicker-day.disabled:hover,
.snap-grid.dark .snap-datepicker-day.disabled:hover,
body.dark .snap-datepicker-day.disabled:hover {
    background: #2A2F37;
    border: none;
}

/* Action Buttons Section */
.snap-datepicker-buttons {
    display: flex;
    gap: 16px;
    width: 232px;
}

.snap-datepicker-clear-btn {
    width: 112px;
    height: 40px;
    background: #F5F5F5;
    border: none;
    border-radius: 4px;
    font-family: 'Amazon Ember', Arial, sans-serif;
    font-size: 12px;
    font-weight: 700;
    line-height: 2em;
    color: #888888;
    cursor: pointer;
}

.snap-datepicker-clear-btn:hover {
    background: #E5E5E5;
}

.snap-datepicker-today-btn {
    width: 112px;
    height: 40px;
    background: var(--action-btn-bg);
    border: none;
    border-radius: 4px;
    font-family: 'Amazon Ember', Arial, sans-serif;
    font-size: 12px;
    font-weight: 700;
    line-height: 2em;
    color: #FFFFFF;
    cursor: pointer;
}

.snap-datepicker-today-btn:hover {
    background: var(--action-btn-hover);
}

/* Dark Mode Buttons */
[data-theme="dark"] .snap-datepicker-clear-btn,
.snap-grid.dark .snap-datepicker-clear-btn,
body.dark .snap-datepicker-clear-btn {
    background: #2A2F37;
}

[data-theme="dark"] .snap-datepicker-clear-btn:hover,
.snap-grid.dark .snap-datepicker-clear-btn:hover,
body.dark .snap-datepicker-clear-btn:hover {
    background: #3A3F47;
}

/* Additional Dark Theme Support */
[data-theme="dark"] .snap-datepicker-dropdown-month .dropdown-menu,
[data-theme="dark"] .snap-datepicker-dropdown-year .dropdown-menu,
.snap-grid.dark .snap-datepicker-dropdown-month .dropdown-menu,
.snap-grid.dark .snap-datepicker-dropdown-year .dropdown-menu,
body.dark .snap-datepicker-dropdown-month .dropdown-menu,
body.dark .snap-datepicker-dropdown-year .dropdown-menu {
    background: #1A1D23;
    border-color: #2F3341;
}

/* Override any popup wrapper styles that might interfere */
.snap-datepicker-popup .snap-datepicker-dropdown,
.snap-datepicker-popup .snap-datepicker-container {
    box-shadow: none !important;
    border: none !important;
    border-radius: 0 !important;
    background: transparent !important;
}

.snap-datepicker-popup .snap-datepicker-container {
    border: 1px solid #E9EBF2 !important;
    border-radius: 8px !important;
    background: #FFFFFF !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

[data-theme="dark"] .snap-datepicker-popup .snap-datepicker-container,
.snap-grid.dark .snap-datepicker-popup .snap-datepicker-container,
body.dark .snap-datepicker-popup .snap-datepicker-container {
    border: 1px solid #2F3341 !important;
    background: #1A1D23 !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

/* Additional Light Mode Overrides */
[data-theme="light"] .snap-datepicker-dropdown-month .dropdown-item.selected,
[data-theme="light"] .snap-datepicker-dropdown-year .dropdown-item.selected {
    background: #F3F4F6;
    color: var(--action-btn-bg);
}

[data-theme="light"] .snap-datepicker-day.disabled {
    background: #FFFFFF;
    color: #C4C4C4;
}

[data-theme="light"] .snap-datepicker-today-btn {
    background: var(--action-btn-bg);
    color: #FFFFFF;
}

[data-theme="light"] .snap-datepicker-today-btn:hover {
    background: var(--action-btn-hover);
}

/* Responsive Design */
@media (max-width: 480px) {
    .snap-datepicker-container {
        width: 280px;
        padding: 16px;
    }

    .snap-datepicker-selector-section {
        width: 100%;
        flex-direction: column;
        gap: 8px;
    }

    .snap-datepicker-dropdown-month,
    .snap-datepicker-dropdown-year {
        width: 100%;
    }

    .snap-datepicker-day,
    .snap-datepicker-day-header {
        width: 24px;
        height: 24px;
        font-size: 12px;
    }

    .snap-datepicker-row {
        gap: 4px;
    }

    .snap-datepicker-header-row {
        gap: 4px;
    }

    .snap-datepicker-buttons {
        width: 100%;
        flex-direction: column;
    }

    .snap-datepicker-clear-btn,
    .snap-datepicker-today-btn {
        width: 100%;
    }
}

/* Date Range Picker Styles */
.snap-daterange-wrapper {
    display: flex;
    gap: 12px;
    width: 100%;
}

.snap-daterange-input-wrapper {
    flex: 1;
    min-width: 0;
}

/* Responsive date range */
@media (max-width: 600px) {
    .snap-daterange-wrapper {
        flex-direction: column;
        gap: 8px;
    }
}

/* Scrollbar Styling for Dropdown Menus - Matching snap-grid styles */
.snap-datepicker-dropdown-month .dropdown-menu,
.snap-datepicker-dropdown-year .dropdown-menu {
    /* Custom scrollbar styling */
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) transparent;
}

/* Webkit scrollbar styling for dropdown menu */
.snap-datepicker-dropdown-month .dropdown-menu::-webkit-scrollbar,
.snap-datepicker-dropdown-year .dropdown-menu::-webkit-scrollbar {
    width: 6px;
}

.snap-datepicker-dropdown-month .dropdown-menu::-webkit-scrollbar-track,
.snap-datepicker-dropdown-year .dropdown-menu::-webkit-scrollbar-track {
    background: transparent;
}

.snap-datepicker-dropdown-month .dropdown-menu::-webkit-scrollbar-thumb,
.snap-datepicker-dropdown-year .dropdown-menu::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.snap-datepicker-dropdown-month .dropdown-menu::-webkit-scrollbar-thumb:hover,
.snap-datepicker-dropdown-year .dropdown-menu::-webkit-scrollbar-thumb:hover {
    background: var(--text-primary);
}

/* Dark mode scrollbar for dropdown menu - standardized colors */
[data-theme="dark"] .snap-datepicker-dropdown-month .dropdown-menu,
[data-theme="dark"] .snap-datepicker-dropdown-year .dropdown-menu,
.snap-grid.dark .snap-datepicker-dropdown-month .dropdown-menu,
.snap-grid.dark .snap-datepicker-dropdown-year .dropdown-menu,
body.dark .snap-datepicker-dropdown-month .dropdown-menu,
body.dark .snap-datepicker-dropdown-year .dropdown-menu {
    scrollbar-color: #2F3341 transparent !important;
}

[data-theme="dark"] .snap-datepicker-dropdown-month .dropdown-menu::-webkit-scrollbar-thumb,
[data-theme="dark"] .snap-datepicker-dropdown-year .dropdown-menu::-webkit-scrollbar-thumb,
.snap-grid.dark .snap-datepicker-dropdown-month .dropdown-menu::-webkit-scrollbar-thumb,
.snap-grid.dark .snap-datepicker-dropdown-year .dropdown-menu::-webkit-scrollbar-thumb,
body.dark .snap-datepicker-dropdown-month .dropdown-menu::-webkit-scrollbar-thumb,
body.dark .snap-datepicker-dropdown-year .dropdown-menu::-webkit-scrollbar-thumb {
    background: #2F3341 !important;
}

[data-theme="dark"] .snap-datepicker-dropdown-month .dropdown-menu::-webkit-scrollbar-thumb:hover,
[data-theme="dark"] .snap-datepicker-dropdown-year .dropdown-menu::-webkit-scrollbar-thumb:hover,
.snap-grid.dark .snap-datepicker-dropdown-month .dropdown-menu::-webkit-scrollbar-thumb:hover,
.snap-grid.dark .snap-datepicker-dropdown-year .dropdown-menu::-webkit-scrollbar-thumb:hover,
body.dark .snap-datepicker-dropdown-month .dropdown-menu::-webkit-scrollbar-thumb:hover,
body.dark .snap-datepicker-dropdown-year .dropdown-menu::-webkit-scrollbar-thumb:hover {
    background: #B4B9C5 !important;
}
